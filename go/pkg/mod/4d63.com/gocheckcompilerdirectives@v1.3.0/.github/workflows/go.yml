name: Go

on:
  push:
    branches:
    - main
  pull_request:
  release:
    types:
    - published

jobs:

  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - run: go test -v ./...

  build-and-upload:
    needs: [test]
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - if: ${{ github.event_name != 'release' }}
      uses: goreleaser/goreleaser-action@90a3faa9d0182683851fbfa97ca1a2cb983bfca3
      with:
        # either 'goreleaser' (default) or 'goreleaser-pro'
        distribution: goreleaser
        # 'latest', 'nightly', or a semver
        version: '~> v2'
        args: release --clean --snapshot
    - if: ${{ github.event_name == 'release' }}
      uses: goreleaser/goreleaser-action@90a3faa9d0182683851fbfa97ca1a2cb983bfca3
      with:
        args: release --rm-dist
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    - uses: actions/upload-artifact@v4
      with:
        name: binaries
        path: dist/*

linters:
  enable:
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
    - containedctx
    - contextcheck
    # - cyclop
    - decorder
    - depguard
    - dogsled
    - dupl
    - durationcheck
    - errcheck
    - errchkjson
    - errname
    - errorlint
    - execinquery
    - exhaustive
    # - exhaustruct
    - exportloopref
    - forbidigo
    - forcetypeassert
    - funlen
    - gci
    # - gochecknoglobals
    - gochecknoinits
    # - gocognit
    - goconst
    - gocritic
    - gocyclo
    - godot
    - godox
    - goerr113
    - gofmt
    - gofumpt
    - goheader
    - goimports
    - gomnd
    - gomoddirectives
    - gomodguard
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - grouper
    - importas
    - ineffassign
    - interfacebloat
    - ireturn
    - lll
    - maintidx
    - makezero
    - misspell
    - nakedret
    - nestif
    - nilerr
    # - nilnil
    # - nlreturn
    - noctx
    - nolintlint
    - nonamedreturns
    - nosprintfhostport
    # - paralleltest
    - prealloc
    - predeclared
    - promlinter
    - reassign
    - revive
    - staticcheck
    - stylecheck
    - tagliatelle
    - tenv
    - testpackage
    - thelper
    - tparallel
    - typecheck
    - unconvert
    - unparam
    - unused
    - usestdlibvars
    - varnamelen
    - whitespace
    - wrapcheck
    # - wsl

module github.com/go-delve/delve

go 1.22.0

require (
	github.com/cilium/ebpf v0.11.0
	github.com/cosiner/argv v0.1.0
	github.com/creack/pty v1.1.20
	github.com/derekparker/trie v0.0.0-20230829180723-39f4de51ef7d
	github.com/go-delve/liner v1.2.3-0.20231231155935-4726ab1d7f62
	github.com/google/go-dap v0.12.0
	github.com/hashicorp/golang-lru v1.0.2
	github.com/mattn/go-colorable v0.1.13
	github.com/mattn/go-isatty v0.0.20
	github.com/spf13/cobra v1.9.1
	github.com/spf13/pflag v1.0.6
	go.starlark.net v0.0.0-20231101134539-556fd59b42f6
	golang.org/x/arch v0.11.0
	golang.org/x/sys v0.26.0
	golang.org/x/telemetry v0.0.0-20241106142447-58a1122356f5
	golang.org/x/tools v0.26.0
	gopkg.in/yaml.v3 v3.0.1
)

require (
	github.com/cpuguy83/go-md2man/v2 v2.0.6 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/mattn/go-runewidth v0.0.13 // indirect
	github.com/rivo/uniseg v0.2.0 // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	golang.org/x/exp v0.0.0-20230224173230-c95f2b4c22f2 // indirect
	golang.org/x/mod v0.21.0 // indirect
	golang.org/x/sync v0.8.0 // indirect
)

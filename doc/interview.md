# 招聘信息

## 招聘岗位

### cuda开发工程师
1. 根据需求，设计并实现基于GPU的功能实现方案或优化提速方案； 
2. 完成项目模块设计、编码、测试、优化和维护工作；负责技术文档的编写； 
3. 解决产品在测试和应用过程中相关的问题；
4. 完成领导布置的其他相关工作。
岗位要求：
1.  精通CUDA、C/C++语言，了解CUDA编程的优化技巧;
2.  熟悉多线程的代码调试流程及性能分析工具；
3.  精通面向对象的编程理论，掌握常见的算法和数据结构知识; 
4.  较强的代码结构设计能力，熟练使用git、ssh等工具； 

### CUDA Kernel开发工程师
职位描述:
1、针对GPU硬件架构（NVIDIA Ampere/Hopper/Blackwell等），设计并实现深度学习框架中高性能算子（Conv/GEMM/Attention等），覆盖训练与推理场景，主导算子性能达到硬件算力峰值；
2、基于CUTLASS/CUTE抽象库，开发定制化算子，结合Tensor Core、Warp Specialization等硬件特性，实现算子极致优化；
3、探索Winograd、FFT、Structured Sparsity等算法在GPU上的高效实现，解决复杂计算模式下的访存/计算瓶颈；
4、构建算子性能自动化分析工具链（Nsight Compute/Systems），建立性能建模与优化方法论。

职位要求:
1、CUDA专家级开发经验：
     a.精通GPU内存模型（Global/Shared/Register/L2 Cache）、SM架构、Warp调度机制；
     b.熟悉指令级优化（PTX/SASS调优、LDGSTS指令、异步拷贝与计算流水）；
     c.掌握CUDA高级特性（Cooperative Groups、Atomic锁竞争规避、Stream并发）；
     d.具备Tensor Core编程经验（mma.sync指令、Warp矩阵计算抽象）。

2、高性能计算优化经验：
     a.深入理解GEMM优化技术（Double Buffering、Software Pipeline、Bank Conflict消除）；
     b.熟悉CUTLASS开源库架构，能基于CUTLASS 2.x/3.x扩展定制化GEMM/Conv算法；
     c.掌握Kernel性能分析方法（Roofline模型、Occupancy计算、指令吞吐/延迟分析）。

3、深度学习领域背景：
     a.熟悉PyTorch/TensorFlow等框架的算子实现机制（如ATen、TorchScript）；
     b.具备推理引擎优化经验（TensorRT/TVM优化、INT8量化算子实现）。
来源：BOSS直聘
链接：https://www.zhipin.com/web/geek/jobs?query=cuda%E5%BC%80%E5%8F%91&city=*********
# 招聘信息

## 招聘岗位

### cuda开发工程师
1. 根据需求，设计并实现基于GPU的功能实现方案或优化提速方案； 
2. 完成项目模块设计、编码、测试、优化和维护工作；负责技术文档的编写； 
3. 解决产品在测试和应用过程中相关的问题；
4. 完成领导布置的其他相关工作。
岗位要求：
1.  精通CUDA、C/C++语言，了解CUDA编程的优化技巧;
2.  熟悉多线程的代码调试流程及性能分析工具；
3.  精通面向对象的编程理论，掌握常见的算法和数据结构知识; 
4.  较强的代码结构设计能力，熟练使用git、ssh等工具； 

### CUDA Kernel开发工程师
职位描述:
1、针对GPU硬件架构（NVIDIA Ampere/Hopper/Blackwell等），设计并实现深度学习框架中高性能算子（Conv/GEMM/Attention等），覆盖训练与推理场景，主导算子性能达到硬件算力峰值；
2、基于CUTLASS/CUTE抽象库，开发定制化算子，结合Tensor Core、Warp Specialization等硬件特性，实现算子极致优化；
3、探索Winograd、FFT、Structured Sparsity等算法在GPU上的高效实现，解决复杂计算模式下的访存/计算瓶颈；
4、构建算子性能自动化分析工具链（Nsight Compute/Systems），建立性能建模与优化方法论。

职位要求:
1、CUDA专家级开发经验：
     a.精通GPU内存模型（Global/Shared/Register/L2 Cache）、SM架构、Warp调度机制；
     b.熟悉指令级优化（PTX/SASS调优、LDGSTS指令、异步拷贝与计算流水）；
     c.掌握CUDA高级特性（Cooperative Groups、Atomic锁竞争规避、Stream并发）；
     d.具备Tensor Core编程经验（mma.sync指令、Warp矩阵计算抽象）。

2、高性能计算优化经验：
     a.深入理解GEMM优化技术（Double Buffering、Software Pipeline、Bank Conflict消除）；
     b.熟悉CUTLASS开源库架构，能基于CUTLASS 2.x/3.x扩展定制化GEMM/Conv算法；
     c.掌握Kernel性能分析方法（Roofline模型、Occupancy计算、指令吞吐/延迟分析）。

3、深度学习领域背景：
     a.熟悉PyTorch/TensorFlow等框架的算子实现机制（如ATen、TorchScript）；
     b.具备推理引擎优化经验（TensorRT/TVM优化、INT8量化算子实现）。
### cuda工程师
岗位职责：
1、基于CUDA平台进行高性能计算相关软件的设计与开发，优化计算性能；
2、使用GPU编程模型（如CUDA C/C++）对算法进行并行加速与性能调优；
3、与算法工程师协作，将机器学习/图像处理/科学计算等算法移植并优化到GPU平台；
4、编写GPU模块相关文档，参与技术规范的制定；
5、跟踪GPU相关的新技术，持续提升系统性能与稳定性；
6、分析系统瓶颈，定位性能问题并提出优化方案；
7、支持公司其他部门在GPU计算方面的技术需求，如提供接口或技术文档说明。

任职资格：
1、计算机、数学等相关专业本科及以上学历；
2、熟练掌握CUDA编程，理解GPU架构及CUDA内存模型；
3、有2年以上C/C++开发经验；有1年以上图像处理的GPU优化经验；
4、较强的问题分析能力与独立解决问题能力；良好的团队合作精神和沟通协调能力。
